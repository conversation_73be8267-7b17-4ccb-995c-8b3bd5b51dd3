#include "Ba.hpp"
#include <fstream>
#include <ctime>
#include <chrono>
#include <sys/types.h>
#include <sys/socket.h>
#include <sys/un.h>
#include <fstream>
#include "utility.hpp"

using namespace std;

#define DIO24DevFILE "/tmp/24DIO"
#define DIO24Log "/tmp/24dio"
#define DIO24DevFILEBAK "/opt/24DIODev"

#define AlarmFILE "/tmp/ALARMFILE"
#define AlarmLog "/tmp/alarmfile"
#define AlarmFILEBAK "/opt/ALARMFILEBAK"

#define ConditionFILE "/tmp/CONDITIONFILE"
#define ConditionLog "/tmp/conditionfile"
#define ConditionFILEBAK "/opt/CONDITIONFILEBAK"

#define MSGCALLEEFILE "/tmp/MSGCALLEEFILE"
#define MSGCALLEELog "/tmp/msgcalleefile"
#define MSGCALLEEFILEBAK "/opt/MSGCALLFILEBAK"

#define MYACCOUNTFILE "/tmp/MY<PERSON>COUNTFILE"
#define MYACCOUNTLog "/tmp/myaccountfile"
#define MYACCOUNTFILEBAK "/opt/MY<PERSON>COUNTFILEBAK"

#define IPFILE "/tmp/IPFILE"
#define IPLog "/tmp/ipfile"
#define IPFILEBAK "/opt/IPFILEBAK"

#define PHONEFILE "/tmp/PHONEFILE"
#define PHONELog "/tmp/phonefile"
#define PHONEFILEBAK "/opt/PHONEFILEBAK"

#define TIMINGFILE "/tmp/TIMINGFILE"
#define TIMINGLog "/tmp/timingfile"
#define TIMINGFILEBAK "/opt/TIMINGFILEBAK"

#define BAIPFILE "/tmp/BAIPFILE"
#define BAIPLog "/tmp/baipfile"

#define IPALIVEFILE "/tmp/IPALIVEFILE"
#define IPALIVELog "/tmp/ipalivefile"

#define IPPINGFILE "/tmp/ipping"

#define DOORCARDFILE "/tmp/DOORCARDFILE"
#define DOORCARDLog "/tmp/doorcardfile"

#define elecNodeFILE "/root/ELECNODEFILE"

#define SIP_STATUS "/tmp/SIP_STATUS"
#define SIP_STATUS_LOG "/tmp/SIP_STATUS_LOG"

#define SOSMAPPINGFILE "/tmp/SOSMAPPINGFILE"

Ba::Ba()
{
    rank = 1;
    isCenter = false;

    use_backup = false;
    // stringstream wgetStringStream;
    // wgetStringStream << "/opt/marco_service/restart.sh &";
    // cout << wgetStringStream.str().c_str();
    // system(wgetStringStream.str().c_str());
}

Ba::~Ba()
{

}

void Ba::do_info(AEvent* p_event)
{
  WSendMsg msg;

  stringstream ss;

  ss << "option=com_floor&task=sroots.send_info_msg&msg_context="<<p_event->msg_context;

  msg.set("/index.php","/tmp/send_info","/tmp/SEND_INFO",false,true);
  msg.set_local(true);
  msg.set_remote(true);
  msg.set_post(ss.str());
  add_sendMessage(&msg);
  //msg.do_send();


}
void Ba::setElecChildrenNodes(int parent_id,int slave_id, int index, long kwh)
{
  for(vector<elecNode*>::iterator iter=allElecNodes.begin(); iter!=allElecNodes.end();iter++ )
  {
    if ((*iter)->getEnableStatus() && (*iter)->getPid() == parent_id) {
      if ((*iter)->getIndex() == index && (*iter)->m_addr == slave_id) {

        // cout << "setElecChildrenNodes: kwh:" <<  kwh << ",index: " << (*iter)->getIndex() << ",slave:" << (*iter)->m_addr << endl;
        (*iter)->setKWH(kwh);
        (*iter)->updateMessageFromBa();
      }
    }
  }
}
void Ba::setElecChildrenNodes(int parent_id,int slave_id, int index, long kwh, int v1, int v2, int v3, int i1, int i2, int i3, int freq, int pf, int kvar, int kw)
{
  for(vector<elecNode*>::iterator iter=allElecNodes.begin(); iter!=allElecNodes.end();iter++ )
  {
    if ((*iter)->getEnableStatus() && (*iter)->getPid() == parent_id) {
      if ((*iter)->getIndex() == index && (*iter)->m_addr == slave_id) {

        // cout << "setElecChildrenNodes: kwh:" <<  kwh << ",index: " << (*iter)->getIndex() << ",slave:" << (*iter)->m_addr << endl;
        (*iter)->setElecNode(kwh, v1, v2, v3, i1, i2, i3, freq, pf, kvar, kw);
        (*iter)->updateMessageFromBa();
      }
    }
  }
}
void Ba::addElecNodes(elecNode* p_node)
{
    allElecNodes.push_back(p_node);
}
void Ba::do_message(AEvent* p_event)
{
  WSendMsg msg;

  stringstream ss;

  ss << "option=com_floor&task=sroots.send_alarm_msg&msg_type="<<p_event->msg_type<<"&msg_name="<<p_event->msg_name<<"&msg_number="<<p_event->msg_number<<"&msg_context="<<p_event->msg_context;

  msg.set("/index.php","/tmp/send_alarm_msg","/tmp/SEND_ALARM_MSG",false,true);
  msg.set_local(true);
  msg.set_remote(false);
  msg.set_post(ss.str());
  add_sendMessage(&msg);
  //msg.do_send();


}

string Ba::get_local_ipaddr()
{
    return "http://127.0.0.1";
    //return "127.0.0.1";
}
string Ba::get_client_ipaddr()
{
    return "https://"+client_ip;
    //return "127.0.0.1";
}

float Ba::calcMainTotal(int id,float kw)
{
    return elecMain.calcTotal(id,kw);
}
ANode* Ba::findNodeById(int id)
{
    for(auto& node : allNodes)
    {
      //cout<<"\n"<<typeid(*this).name()<<"::"<<__func__<<" "<<node->getId()<<endl;
      if(node->getId() == id)
      {
        return node;
      }
    }

    return 0;
}

bool Ba::sendDoorCardMessage(AEvent* p_event)
{
  std::stringstream ss;

  ss << "curl "<<get_local_ipaddr();
  ss << "/index.php?option=\"com_floor&task=sroots.getRecordFile&ipaddr="<<p_event->ipaddr<<"&timeout=5"<<"&name="<<p_event->path<<"&id="<<p_event->id<<"&type="<<p_event->type<<"\" -o /tmp/DOORCARD > /dev/null 2> /dev/null &";

  system(ss.str().c_str());
  cout <<"\n"<<typeid(*this).name()<<"::"<<__func__<<endl;
  cout<<ss.str()<<endl;

}
void Ba::doorCardEvent(char *p_str_message)
{
  char* p_number = &p_str_message[32];
  p_str_message[43] = 0;
  string str_door = p_number;
  doorCardEvent(str_door);
}
void Ba::doorCardEvent(std::string number)
{
  cout <<"\n"<<typeid(*this).name()<<"::"<<__func__<<" "<<number<<endl;
  for(auto& dev : allDoorCards)
  {
    if(dev->event(number))
    {
      break;
    }
    //dev->update(json);
  }

}
void Ba::waitRecord(std::string ipaddr,int timeout)
{

  std::stringstream ss;

  ss << "curl "<<get_local_ipaddr();
  ss << "/index.php?option=\"com_floor&task=sroots.getRecordFile&ipaddr="<<ipaddr<<"&timeout="<<timeout<<"&name="<<myAcc.path<<"\"  -o /tmp/WAITRECORD > /dev/null 2> /dev/null &";

  system(ss.str().c_str());
  cout <<"\n"<<typeid(*this).name()<<"::"<<__func__<<endl;
  cout<<ss.str()<<endl;

}
void Ba::doUpdatePhone(std::string json)
{
    for(auto& dev : phoneDevices)
    {
      dev->update(json);
    }
}
bool Ba::updateDOMessage(AEvent* p_event)
{
  bool ret;

  ret = true;

  //snprintf(buf,200,"wget http://%s/index.php?option=\"com_floor&task=sroots.update_do&value=%c&ipaddr=%d&index=%d&note=%s\" -t3 -o /tmp/logdo -O /tmp/LOGDO",get_server_ipaddr(),value,m_id,index,m_ipaddr.c_str());
  std::stringstream ss;

  ss << "/index.php?option=\"com_floor&task=sroots.update_do2&value="<<p_event->charStatus<<"&ipaddr="<<p_event->id<<"&index="<<p_event->index<<"&note="<<p_event->pid<<"\" ";

  WSendMsg msg;

  msg.set(ss.str(),"/tmp/logdo","/tmp/LOGDO",false,true);

  add_sendMessage(&msg);

  return ret;

}
bool Ba::sendAlarmMessage(AEvent* p_event)
{
  bool ret;

  ret = false;

  std::stringstream ss;

  ss << "/index.php?option=\"com_floor&task=sroots.dio_alarm2&is_alarm="<<p_event->status<<"&ipaddr="<<p_event->id<<"&index="<<p_event->index<<"&note="<<p_event->pid<<"\" ";

  WSendMsg msg;

  msg.set(ss.str(),"/tmp/sendmsg","/tmp/SENDMSG",false,true);
  add_sendMessage(&msg);
  //ret = check_http_result("/tmp/sendmsg");
  return ret;

}
bool Ba::updateDIMessage(AEvent* p_event)
{
  bool ret;

  ret = true;

  std::stringstream ss;

  ss << "/index.php?option=\"com_floor&task=sroots.update_di2&value="<<p_event->charStatus<<"&ipaddr="<<p_event->id<<"&index="<<p_event->index<<"&note="<<p_event->pid<<"\"  ";

  WSendMsg msg;

  msg.set(ss.str(),"/tmp/update_di","/tmp/UPDATE_DI",false,true);
  add_sendMessage(&msg);

  return ret;

}
bool Ba::sendCondAlarmMessage(AEvent* p_event)
{
  bool ret;

  ret = false;
  std::stringstream ss;

  ss << "/index.php?option=\"com_floor&task=sroots.cond_alarm2&value="<<p_event->alarm<<"&ipaddr="<<p_event->dio_device_di_c<<"&index="<<p_event->dio_index_di_c<<"&note="<<p_event->dio_device_di_c_p<<"\" ";

  WSendMsg msg;

  msg.set(ss.str(),"/tmp/condalarm","/tmp/CONDALARM",false,true);

  add_sendMessage(&msg);
  return ret;
}
bool Ba::sendConditionMessage(AEvent* p_event)
{
  bool ret;

  ret = false;

  std::stringstream ss;

  ss << "/index.php?option=\"com_floor&task=sroots.dio_condition2&is_alarm="<<static_cast<std::underlying_type<ECondState>::type>(p_event->condState)<<"&ipaddr="<<p_event->dio_device_di_c<<"&index="<<p_event->dio_index_di_c<<"&note="<<p_event->dio_device_di_c_p<<"\" ";

  WSendMsg msg;

  msg.set(ss.str(),"/tmp/sendcondition","/tmp/SENDCONDITION",false,true);

  add_sendMessage(&msg);
  //cout << getNowTime();
  //cout<<ss.str()<<endl;
  //system(ss.str().c_str());
  return ret;
}
void Ba::addNodeToList(ANode* p_node)
{
    //cout<<"\n"<<__func__<<endl;
    allNodes.push_back(p_node);
    //node.print();
}

void Ba::do_di_release(AEvent* p_event)
{
  //alarmMessage(p_event->name,p_event->state);
  clear_alarm(p_event->alarmdir, p_event->alarm,true);
  std::stringstream ss;

  ss << "/index.php?option=\"com_floor&task=sroots.dio_alarm2&is_alarm="<<p_event->status<<"&ipaddr="<<p_event->id<<"&index="<<p_event->index<<"&note="<<p_event->pid<<"\" ";

  WSendMsg msg;

  msg.set(ss.str(),"/tmp/sendmsg","/tmp/SENDMSG",false,true);
  add_sendMessage(&msg);
}

void Ba::do_di_event(AEvent* p_event)
{
  //alarmMessage(p_event->name,p_event->state);
  do_alarm(p_event->alarmdir,p_event->alarm);

  std::stringstream ss;

  ss << "/index.php?option=\"com_floor&task=sroots.dio_alarm2&is_alarm="<<p_event->status<<"&ipaddr="<<p_event->id<<"&index="<<p_event->index<<"&note="<<p_event->pid<<"\" ";

  WSendMsg msg;

  msg.set(ss.str(),"/tmp/sendmsg","/tmp/SENDMSG",false,true);
  add_sendMessage(&msg);
}
std::vector<Host>& Ba::getHosts()
{
  return allBaIPs;
}
bool Ba::isFirstAlive()
{
  if(firstCenterIpaddr.size() == 0 || isCenter == false || rank == 1) return false;

  stringstream ss;

  ss << "curl https://"<<firstCenterIpaddr<<"/index.php?option=\"com_floor&task=sroots.alive\" -k --connect-timeout 3 -o "<<IPALIVEFILE;

  int ret;
  ret = system(ss.str().c_str());

 
  return (ret==0);
}
string Ba::findFirstCenter()
{
    bool ret;

    string ipaddr;
    ret = false;
    for(auto& ip : allBaIPs)
    {
      if(ip.isFirstCenter(&ipaddr))
      {
        ret = true;
        break;
      }
    }

    firstCenterIpaddr = ipaddr;
    return ipaddr;

}
bool Ba::isCenterRole(int *p_rank)
{
    bool ret;

    ret = false;
    for(auto& ip : allBaIPs)
    {
      if(ip.isCenter(p_rank))
      {
        ret = true;
        break;
      }
    }

    rank = *p_rank;
    isCenter = ret;

    cout<<"\nBa::"<<__func__<<" "<<isCenter<<endl;
    return ret;

}
bool Ba::isBaRole()
{
    getAndSetBaData();

    bool ret;

    ret = false;
    for(auto& ip : allBaIPs)
    {
      if(ip.isBa())
      {
        ret = true;
        break;
      }
    }

    return ret;

}
void Ba::check_ipaddr()
{
  getAndSetBaData();


}

void Ba::saveElecKWH()
{
  Json::Value array;
  Json::Value json_id;
  string json;

   Json::FastWriter writer;

   for(auto& node : allElecNodes)
   {
     json_id["id"] = node->m_id;
     json_id["kwh"] = static_cast<Json::Int64>(node->getKWH()); 

     array.append(json_id);
   }

   json = writer.write(array);

   writeToFile(elecNodeFILE,json.c_str());

}
void Ba::doUpdateElecKHW(Json::Value it)
{
  int id;
  long kwh;

  set_int_value(it,&id,"id");
  set_int64_value(it,&kwh,"kwh");

  for(auto &node : allElecNodes)
  {
    if(node->m_id == id)
    {
      node->setKWH(kwh);
      break;
    }
  }
}
void Ba::doUpdateElecKHW(string json)
{
  Json::Reader reader;
  Json::Value value;
  //std::cout << "Input: " << json << std::endl;
  if (reader.parse(json, value)) {
      //std::cout << "parsing: " << value ;//<< std::endl;
      // with -std=c++11
      std::cout << "\n---" << std::endl;
      for (auto it : value) {
          cout << "id "<<it["id"]<<" kwh "<<it["kwh"]<<endl;

          doUpdateElecKHW(it);

      }

  }

}

void Ba::updateElecKHW()
{
  string str_json = readAllToJson(elecNodeFILE);
  doUpdateElecKHW(str_json);
}

void Ba::update_sip_status()
{
  int ret;

  ret = -1;
  for(auto& host : allBaIPs)
  {
    if(host.isBaType())
    {
      stringstream ss;

      //ss << "wget "<<host.getIpaddr()<<"/index.php?option=\"com_floor&task=sroots.update_sip_status\" -t3 --connect-timeout=3 -o "<< SIP_STATUS_LOG <<" -O "<<SIP_STATUS;
      ss << "curl https://"<<host.getIpaddr()<<"/index.php?option=\"com_floor&task=sroots.update_sip_status\"  -k --connect-timeout 3 -o "<< SIP_STATUS;

      ret = system(ss.str().c_str());
      cout<<"\n"<<typeid(*this).name()<<"::"<<__func__<<" "<<ret<<" "<<ss.str()<<endl;
      fflush(stdout);
    }
  }

}
void Ba::getData()
{
  read_jobs_from_file();
  update_sip_status();
  getAndSetSOSData();
  getAndSetDIOData();
  getAndSetAlarmData();
  getAndSetConditionData();

  getAndSetCalleeData();
  getAndSetMyAccountData();
  getAndSetIPDeviceData();
  getAndSetPhoneDeviceData();
  getAndSetTimingData();

  getAndSetDoorCardData();

  updateElecKHW();
  clear_jobs();
}

void Ba::doIPDev(EDIOState status)
{
  if(ipDevices.size() == 0)    return;

  ofstream file;      //宣告fstream物件

  file.open(IPPINGFILE, ios::out | ios::trunc);

  if(!file)    return;

  for(auto& dev : ipDevices)
  {
    dev->writeToFile(status,file);
  }

  file.close();

  stringstream ss;

  string ping_status = "/tmp/ping3";

  EDIOState update_status;

  update_status = EDIOState::RELEASE;
  if(status == EDIOState::RELEASE)
  {
      update_status = EDIOState::ALARM;
      ping_status = "/tmp/ping1";
  }

  ss << "fping -f " << IPPINGFILE << " > "<<ping_status;
  system(ss.str().c_str());

  ifstream infile(ping_status);

  vec_string ip_string;

  ip_string.empty();
  getPingStatus(infile,status,ip_string);

  if(ip_string.size() == 0)
      return;

  cout<<"\n"<<typeid(*this).name()<<"::"<<__func__<<" "<<ping_status;
  cout<<" "<<ip_string.size()<<endl;
  for(auto& str : ip_string)
  {
    cout<<" "<<str;
  }

  cout<<endl;
  map_int_string ids_map;

  for(auto& str : ip_string)
  {
    //cout<<str<<endl;
    for(auto& dev : ipDevices)
    {
      if(dev->update(str,update_status,ids_map))
          break;
    }

  }

  if(ids_map.size() > 0)
  {
    string json;
    writeIdsToJson(update_status,ids_map,json);

    WSendMsg msg;
    msg.do_send(json);
    sendIPAlarm(update_status,ids_map);
  }

}
void Ba::doPhoneDev()
{
  for(auto& dev : phoneDevices)
  {
    dev->doPhone();

    break;
  }


}

void Ba::getAllCallee()
{
  stringstream ss;

  // 使用 CURL 直接獲取資料到記憶體，不寫入檔案
  ss << "curl "<<get_client_ipaddr()<<"/index.php?option=\"com_floor&task=sroots.getCallee\"  -k --connect-timeout 5";

  system(ss.str().c_str());

  //cout<<ss.str()<<endl;
}

void Ba::getAllBaIP()
{
  stringstream ss;

  ss << "curl "<<get_local_ipaddr()<<"/index.php?option=\"com_floor&task=sroots.getBaList\" --connect-timeout 5 -o "<<BAIPFILE;

  system(ss.str().c_str());
  stringstream wgetStringStream;
  wgetStringStream << "/opt/marco_service/restart.sh";
  cout << wgetStringStream.str().c_str();
  system(wgetStringStream.str().c_str());
  //cout<<ss.str()<<endl;
}

void Ba::getAllDoorCard()
{
  stringstream ss;

  ss << "curl "<<get_client_ipaddr()<<"/index.php?option=\"com_floor&task=sroots.getDoorCards\"  -k --connect-timeout 5 -o "<<DOORCARDFILE;

  system(ss.str().c_str());

  //cout<<ss.str()<<endl;
}


void Ba::getAllTimings()
{
  stringstream ss;

  ss << "curl "<<get_client_ipaddr()<<"/index.php?option=\"com_floor&task=sroots.getTimings\" -k --connect-timeout 5 -o "<<TIMINGFILE;

  system(ss.str().c_str());

  //cout<<ss.str()<<endl;
}

void Ba::getMyAccount()
{
  stringstream ss;

  ss << "curl "<<get_client_ipaddr()<<"/index.php?option=\"com_floor&task=sroots.getMyAccount\"  -k --connect-timeout 5 -o "<<MYACCOUNTFILE;

  system(ss.str().c_str());

  //cout<<ss.str()<<endl;
}
void Ba::getIPDevices()
{
  stringstream ss;

  ss << "curl "<<get_client_ipaddr()<<"/index.php?option=\"com_floor&task=sroots.getIPDirs\" -k --connect-timeout 5 -o "<<IPFILE;

  system(ss.str().c_str());

  //cout<<ss.str()<<endl;
}
void Ba::getPhoneDevices()
{
  stringstream ss;

  ss << "curl "<<get_client_ipaddr()<<"/index.php?option=\"com_floor&task=sroots.getPhones\"  -k --connect-timeout 5 -o "<<PHONEFILE;

  system(ss.str().c_str());

  //cout<<ss.str()<<endl;
}
void Ba::getAllDevice()
{
    bool ret;

    ret = false;
    for(auto& host : allBaIPs)
    {
      if(host.isBaType())
      {
        if(getAllDevice(host.getIpaddr()))
        {
          client_ip = host.getIpaddr();
          ret = true;
          break;
        }
      }
    }

    if(ret == false)
    {
      use_backup = true;
    }
  //cout<<ss.str()<<endl;
}
bool Ba::getAllDevice(string ipaddr)
{
  stringstream ss;
  
  //ss << "wget http://"<<ipaddr<<"/index.php?option=\"com_floor&task=sroots.get24DIODevice\" -t3 --connect-timeout=10 -o "<< DIO24Log <<" -O "<<DIO24DevFILE;

  ss << "curl https://"<<ipaddr<<"/index.php?option=\"com_floor&task=sroots.get24DIODevice\" -k --connect-timeout 30 -o "<<DIO24DevFILE;

  int ret;
  ret = system(ss.str().c_str());

  //return check_http_ret(DIO24Log);
  return (ret == 0);

  //cout<<ss.str()<<endl;
}

void Ba::getAllAlarms()
{
  stringstream ss;

  //ss << "wget "<<get_client_ipaddr()<<"/index.php?option=\"com_floor&task=sroots.getAlarms\" -t3 --connect-timeout=10 -o "<< AlarmLog <<" -O "<<AlarmFILE;
  ss << "curl "<<get_client_ipaddr()<<"/index.php?option=\"com_floor&task=sroots.getAlarms\"  -k --connect-timeout 3 -o "<<AlarmFILE;

  system(ss.str().c_str());
}

void Ba::getAllConditions()
{
  stringstream ss;

  ss << "curl "<<get_client_ipaddr()<<"/index.php?option=\"com_floor&task=sroots.getConditions\"  -k --connect-timeout 5 -o "<<ConditionFILE;

  system(ss.str().c_str());
}


void Ba::addAllTestCallee(string file)
{
  string str_json = readAllToJson(file);
  addAllCallees(str_json);
}

void Ba::addAllTestCondition(string file)
{
  string json = readAllToJson(file);
  addAllConditions(json);
}
void Ba::addAllTestTiming(string file)
{
  string json = readAllToJson(file);
  addAllTimings(json);
}

void Ba::addAllTestIP(string file)
{
  string json = readAllToJson(file);
  addIPDevices(json);
}

void Ba::addAllTestPhone(string file)
{
  string json = readAllToJson(file);
  addPhoneDevices(json);
}
void Ba::addAllTestAlarm(string file)
{
  string json = readAllToJson(file);
  addAllAlarms(json);
}

void Ba::addAllTestDevice(string file)
{
  string json = readAllToJson(file);
  addAllDevice(json);
}

void Ba::doAddDIO24Device(Json::Value it)
{
  int enable;

  set_int_value(it,&enable,"enable");

  if(enable == 0)
  {
    cout <<"\nBa::"<<__func__<<" enable == 0"<<endl;
    return;
  }

  auto it1 = it["devices"];

  for(auto it2 : it1)
  {
      set_int_value(it2,&enable,"enable");

      if(enable == 0)
          continue;

      it2["pid"] = it["id"];
      it2["dio_port"] = it["dio_port"];
      it2["vendor"] = it["vendor"];
      auto dev = std::make_shared<dio24Dev>();// modbusTCPDev dev;
      dev->set(it2);
      allDevs.push_back(dev);
  }

}
void Ba::addModbusTCP(Json::Value it)
{
  int enable;

  set_int_value(it,&enable,"enable");

  if(enable == 0)    return;

      cout<<"\nBa::addModbusTCP"<<endl;
      cout<<it["id"]<<endl;

  auto dev = std::make_shared<modbusTCP>();// modbusTCPDev dev;
  int size = dev->set(it);

  if(size)
      allDevs.push_back(dev);
  //cout<<"\n"<<__func__<<" "<<m_allDevs.size()<<endl;

}

void Ba::addRs485ModbusTCP(Json::Value it)
{
  int enable;

  set_int_value(it,&enable,"enable");

  if(enable == 0)    return;

  auto dev = std::make_shared<modbusTCP>();// modbusTCPDev dev;
  int size = dev->set(it);

  if(size)
      allRS485Devs.push_back(dev);
  //cout<<"\n"<<__func__<<" "<<m_allDevs.size()<<endl;

}
void Ba::addDoorAlarm(Json::Value it)
{
    auto dev = std::make_shared<TopDoorAlarm>();// modbusTCPDev dev;

    dev->set(it);
    allDoorAlarms.push_back(dev);
}
void Ba::doAddAllDevice(Json::Value it)
{
  int vendor ;//= atoi(it["vendor"].asCString());
  set_int_value(it,&vendor,"vendor");

  if(vendor == WEEMA_VENDOR)
  {
      //cout<<"\n"<<it<<endl;
  }
  //cout<<"\n"<<it<<endl;
  if(vendor == WEEMA_VENDOR)
  {
    doAddDIO24Device(it);
  }

  else if(vendor == ADVANTECH_VENDOR)
  {
      addModbusTCP(it);
  }

  else if(vendor == PANASONIC_TWO_LINE_VENDOR)
  {
      addModbusTCP(it);
  }
  else if (vendor == YUNYANG_FIRE_FIGHTING_VENDOR)
  {
      addModbusTCP(it);
  }
  else if (vendor == BAOCHUNG_FIRE_FIGHTING_VENDOR)
  {
      addModbusTCP(it);
  }
  else if (vendor == LIUCHUAN_ELEVATOR_VENDOR)
  {
      addModbusTCP(it);
  }
  else if (vendor == MITSUBISHI_ELEVATOR_VENDOR)
  {
      addModbusTCP(it);
  }
  else if (vendor == FUJI_ELEVATOR_VENDOR)
  {
      addModbusTCP(it);
  }
  else if(vendor == SINEW_VENDOR)
  {
      addModbusTCP(it);
  }

  else if(vendor == SOYAL_VENDOR)
  {
      addModbusTCP(it);
  }

  else if(vendor == ICP_VENDOR)
  {
      addModbusTCP(it);
  }
  else if(vendor == RS485_VENDOR)
  {
      //cout<<"\n"<<typeid(*this).name()<<"::"<<__func__<<" "<<it<<endl;
      addRs485ModbusTCP(it);
  }

  else if(vendor == DOOR_VENDOR)
  {
    addDoorAlarm(it);
  }



}
void Ba::addAllDevice(string json)
{
  Json::Reader reader;
  Json::Value value;
  //std::cout << "Input: " << json << std::endl;
  if (reader.parse(json, value)) {
      //std::cout << "parsing: " << value ;//<< std::endl;
      // with -std=c++11
      std::cout << "\n---" << std::endl;
      for (auto it : value) {
          cout << "Ba::addAllDevice id "<<it["id"]<<" name "<<it["name"] <<" port "<<it["dio_port"]<<" vendor "<<it["vendor"]<<endl;

          doAddAllDevice(it);

      }

  }
}

void Ba::addAllAlarms(string json)
{
  Json::Reader reader;
  Json::Value value;
  //std::cout << "Input: " << json << std::endl;
  if (reader.parse(json, value)) {
      //std::cout << "parsing: " << value ;//<< std::endl;
      // with -std=c++11
      std::cout << "\n---" << std::endl;
      for (auto it : value) {
          cout << "Ba::addAllAlarms id "<<it["id"]<<" name "<<it["name"] <<endl;
          //cout<<it<<endl;

          auto dev = std::make_shared<TopAlarm>();// modbusTCPDev dev;

          dev->set(it);
          allAlarms.push_back(dev);

      }

  }
}

void Ba::update_acc_info()
{
  cout<<"\n"<<typeid(*this).name()<<"::"<<__func__<<endl;
  WSendMsg msg;

  stringstream ss;
  ss << "option=com_floor&task=sroots.update_acc&enable="<<myAcc.ftp_enable<<"&server="<<myAcc.ftp_server<<"&port="<<myAcc.ftp_port<<"&user="<<myAcc.ftp_user<<"&passwd="<<myAcc.ftp_passwd;
  ss << "&sms_username="<<myAcc.sms_username<<"&sms_passwd="<<myAcc.sms_passwd;
  ss << "&rec_sec="<<myAcc.rec_sec;
  ss << "&del_alarm_enable="<<myAcc.del_alarm_enable;
  ss << "&del_alarmdir="<<myAcc.del_alarmdir;
  ss << "&del_alarm="<<myAcc.del_alarm;
  ss << "&limit_alarm_enable="<<myAcc.limit_alarm_enable;
  ss << "&limit_alarmdir="<<myAcc.limit_alarmdir;
  ss << "&limit_alarm="<<myAcc.limit_alarm;

  ss << "&out_alarm_enable="<<myAcc.out_alarm_enable;
  ss << "&out_alarmdir="<<myAcc.out_alarmdir;
  ss << "&out_alarm="<<myAcc.out_alarm;

  ss << "&passwd_error_alarm_enable="<<myAcc.passwd_error_alarm_enable;
  ss << "&passwd_error_alarmdir="<<myAcc.passwd_error_alarmdir;
  ss << "&passwd_error_alarm="<<myAcc.passwd_error_alarm;

  ss << "&username_error_alarm_enable="<<myAcc.username_error_alarm_enable;
  ss << "&username_error_alarmdir="<<myAcc.username_error_alarmdir;
  ss << "&username_error_alarm="<<myAcc.username_error_alarm;
  ss << "&elec_alarm="<<myAcc.elec_kw;

  msg.set("/index.php","/tmp/update_ftp","/tmp/UPDATE_FTP",false,true);
  msg.set_local(true);
  msg.set_remote(false);
  msg.set_post(ss.str());
  add_sendMessage(&msg);
// twgtm
// twgtm
}

void Ba::addMyAccount(string json)
{
  Json::Reader reader;
  Json::Value value;
  //std::cout << "Input: " << json << std::endl;
  if (reader.parse(json, value)) {
      //std::cout << "parsing: " << value ;//<< std::endl;
      // with -std=c++11
      std::cout << "\n---" << std::endl;
      myAcc.set(value);
      elecMain.add(&myAcc);

  }

  update_acc_info();
}

void Ba::addAllTimings(string json)
{

  Json::Reader reader;
  Json::Value value;
  //std::cout << "Input: " << json << std::endl;
  if (reader.parse(json, value)) {
      //std::cout << "parsing: " << value ;//<< std::endl;
      // with -std=c++11
      //myAcc.set(value);

      std::cout << "\n---addAllTimings" << std::endl;
      crontab_to_write(value);

  }
}

void Ba::addAllCallees(string json)
{
  Json::Reader reader;
  Json::Value value;
  //std::cout << "Input: " << json << std::endl;
  if (reader.parse(json, value)) {
      //std::cout << "parsing: " << value ;//<< std::endl;
      // with -std=c++11
      std::cout << "\n---" << std::endl;
      allCallee.set(value);

  }
}

void Ba::addAllDoorCard(string json)
{
  Json::Reader reader;
  Json::Value value;
  //std::cout << "Input: " << json << std::endl;
  if (reader.parse(json, value)) {
      //std::cout << "parsing: " << value ;//<< std::endl;
      // with -std=c++11
      std::cout << "\n---" << std::endl;
      for(auto& it : value)
      {

        auto dev = std::make_shared<TopDoorCard>();// modbusTCPDev dev;
        dev->set(it);
        allDoorCards.push_back(dev);

      }

  }
}


void Ba::addAllBaIP(string json)
{
  Json::Reader reader;
  Json::Value value;
  //std::cout << "Input: " << json << std::endl;
  if (reader.parse(json, value)) {
      //std::cout << "parsing: " << value ;//<< std::endl;
      // with -std=c++11
      std::cout << "\n---" << std::endl;
      for(auto& it : value)
      {
        Host host;
        host.set(it);

        allBaIPs.push_back(host);
      }

  }
}
void Ba::addIPDevices(string json)
{
  Json::Reader reader;
  Json::Value value;
  //std::cout << "Input: " << json << std::endl;
  if (reader.parse(json, value)) {
      //std::cout << "parsing: " << value ;//<< std::endl;
      // with -std=c++11
      std::cout << "\n---" << std::endl;
      for (auto it : value) {
          cout << "Ba::addIPDevices id "<<it["id"]<<" name "<<it["name"] <<endl;

          auto dev = std::make_shared<ipDev>();// modbusTCPDev dev;

          dev->set(it);
          ipDevices.push_back(dev);

      }

  }
}

void Ba::addPhoneDevices(string json)
{
  Json::Reader reader;
  Json::Value value;
  //std::cout << "Input: " << json << std::endl;
  if (reader.parse(json, value)) {
      //std::cout << "parsing: " << value ;//<< std::endl;
      // with -std=c++11
      std::cout << "\n---" << std::endl;
      for (auto it : value) {
          cout << "Ba::addPhoneDevices id "<<it["id"]<<" name "<<it["name"] <<endl;

          auto dev = std::make_shared<phoneDev>();// modbusTCPDev dev;

          dev->set(it);
          phoneDevices.push_back(dev);

      }

  }
}
void Ba::addAllConditions(string json)
{
  //cout<<"\n"<<__func__<<json<<endl;
  Json::Reader reader;
  Json::Value value;
  //std::cout << "Input: " << json << std::endl;
  if (reader.parse(json, value)) {
      //std::cout << "parsing: " << value ;//<< std::endl;
      // with -std=c++11
      std::cout << "\n---" << std::endl;
      for (auto it : value) {
          cout << "Ba::addAllCondition id "<<it["id"]<<" name "<<it["name"] <<endl;
          //cout<<it<<endl;
          auto dev = std::make_shared<TopCondition>();// modbusTCPDev dev;

          dev->set(it);
          allConditions.push_back(dev);

      }

  }
}


void Ba::getAndSetTimingData()
{
  if(use_backup)
  {
      string str_json = readAllToJson(TIMINGFILEBAK);
      addAllDevice(str_json);
      return;
  }
  getAllTimings();

  string str_json = readAllToJson(TIMINGFILE);
  addAllTimings(str_json);

  copy_file(TIMINGFILE,TIMINGFILEBAK);

}
void Ba::getAndSetMyAccountData()
{
  if(use_backup)
  {
      string str_json = readAllToJson(MYACCOUNTFILEBAK);
      addMyAccount(str_json);
      return;
  }
  getMyAccount();

  string str_json = readAllToJson(MYACCOUNTFILE);
  addMyAccount(str_json);

  copy_file(MYACCOUNTFILE,MYACCOUNTFILEBAK);

}

void Ba::getAndSetIPDeviceData()
{
  if(use_backup)
  {
      string str_json = readAllToJson(IPFILEBAK);
      addAllDevice(str_json);
      return;
  }
  getIPDevices();

  string str_json = readAllToJson(IPFILE);
  addIPDevices(str_json);

  copy_file(IPFILE,IPFILEBAK);

}
void Ba::getAndSetPhoneDeviceData()
{
  if(use_backup)
  {
      string str_json = readAllToJson(PHONEFILEBAK);
      addAllDevice(str_json);
      return;
  }
  getPhoneDevices();

  string str_json = readAllToJson(PHONEFILE);
  addPhoneDevices(str_json);

  copy_file(PHONEFILE,PHONEFILEBAK);

}
void Ba::getAndSetCalleeData()
{
  if(use_backup)
  {
      string str_json = readAllToJson(MSGCALLEEFILEBAK);
      addAllDevice(str_json);
      return;
  }

  getAllCallee();

  string str_json = readAllToJson(MSGCALLEEFILE);
  addAllCallees(str_json);

  copy_file(MSGCALLEEFILE,MSGCALLEEFILEBAK);

}

void Ba::getAndSetDoorCardData()
{
  getAllDoorCard();

  string str_json = readAllToJson(DOORCARDFILE);
  addAllDoorCard(str_json);

}


void Ba::getAndSetBaData()
{
  getAllBaIP();

  string str_json = readAllToJson(BAIPFILE);
  addAllBaIP(str_json);

}

void Ba::getAndSetSOSData()
{
    stringstream ss;
    ss << "curl "<<get_local_ipaddr();
    ss << "/index.php?option=\"com_floor&task=sroots.getSOSItems\" --connect-timeout 3  -o "<<SOSMAPPINGFILE<<" > /dev/null 2> /dev/null &";

    cout << "\n" << ss.str();
    fflush(stdout);
    system(ss.str().c_str());

}

void Ba::getAndSetDIOData()
{
  getAllDevice();

  if(use_backup == false)
  {
      string str_json = readAllToJson(DIO24DevFILE);
      addAllDevice(str_json);

      copy_file(DIO24DevFILE,DIO24DevFILEBAK);
  }
  else
  {
    string str_json = readAllToJson(DIO24DevFILEBAK);
    addAllDevice(str_json);

  }

}

void Ba::getAndSetAlarmData()
{
  if(use_backup)
  {
      string str_json = readAllToJson(AlarmFILEBAK);
      addAllDevice(str_json);
      return;
  }

  getAllAlarms();

  string str_json = readAllToJson(AlarmFILE);
  addAllAlarms(str_json);

  copy_file(AlarmFILE,AlarmFILEBAK);
}

void Ba::getAndSetConditionData()
{
  if(use_backup)
  {
      string str_json = readAllToJson(ConditionFILEBAK);
      addAllDevice(str_json);
      return;
  }
  getAllConditions();

  string str_json = readAllToJson(ConditionFILE);
  addAllConditions(str_json);

  copy_file(ConditionFILE,ConditionFILEBAK);
}



void Ba::add_sendMessage(WSendMsg* p_msg)
{
  std::lock_guard<std::mutex> lck(mtx_msg);

  if(p_msg->do_send() == false)
  {
      allSendMsgs.push_back(*p_msg);
  }
}
bool Ba::do_phoneAlarm(std::string number,int stauts)
{
    bool ret;
    ret = false;
    for(auto& alarm : allDoorAlarms)
    {
      if(alarm->do_alarm(number,stauts))
      {
        ret = true;
        break;
      }
    }

    return ret;
}

bool Ba::do_action(int parent,int device,int index,int value,int timeout,bool return_last_state, bool cancel_previous_job)
{
    bool ret;
    ret = false;
    // if (cancel_previous_job)
    // {
    //   cancel_previous_timeout_job(parent,device,index);
    // }
    cout<<"Ba::do_action "<<parent<<" "<<device<<" "<<index<<" "<<value<<" "<<timeout<<" "<<return_last_state<<" "<<cancel_previous_job<<endl;
    fflush(stdout);
    cancel_previous_timeout_job(parent, device, index);
    for(auto& dev : allDevs)
    {
      if(dev->do_action(parent,device,index,value,timeout,return_last_state))
      {
        ret = true;
        break;
      }
    }

    return ret;
}
bool Ba::do_action(int parent,int device,int index,int value,int timeout)
{
    bool ret;
    ret = false;
    cout<<"Ba::do_action1 "<<parent<<" "<<device<<" "<<index<<" "<<value<<" "<<timeout<<endl;
    fflush(stdout);
    cancel_previous_timeout_job(parent, device, index);
    for(auto& dev : allDevs)
    {
      if(dev->do_action(parent,device,index,value,timeout))
      {
        ret = true;
        break;
      }
    }

    return ret;
}
bool Ba::cancel_previous_timeout_job(int parent, int device, int index)
{
    bool ret = false;
    // cout << "ba::cancel_previous_timeout_job" << endl;
    for(auto& dev : allDevs)
    {
      if(dev->cancel_previous_timeout_job(parent, device, index))
      {
        ret = true;
        break;
      }
    }
    return ret;
}
bool Ba::clear_alarm(int alarmdir, int alarm,bool is_do)
{
  bool ret;
  //std::lock_guard<std::mutex> lck(mtx);
  ret = false;
  for(auto& node: allAlarms)
  {
    if(node->clear_alarm(alarmdir,alarm,is_do))
    {
      ret = true;
      break;
    }
  }

  return ret;
}
bool Ba::do_alarm(int alarmdir,int alarm)
{
  bool ret;

  //std::lock_guard<std::mutex> lck(mtx);

  ret = false;
  for(auto& node: allAlarms)
  {
    if(node->do_alarm(alarmdir,alarm))
    {
      ret = true;
      break;
    }
  }

  return ret;
}

string Ba::get_callee_list()
{
  return allCallee.getList();
}
bool Ba::is_di_first_alarm_end(int pid,int id,int index,Condition** p_cond)
{
  bool ret;

  ret = false;
  for(auto & cond : allConditions)
  {
    if(cond->is_di_first_alarm_end(pid,id,index,p_cond))
    {
      ret = true;
      break;
    }
  }

  return ret;
}
bool Ba::is_di_second_alarm_end(int pid,int id,int index,Condition** p_cond)
{
  bool ret;

  ret = false;
  for(auto & cond : allConditions)
  {
    if(cond->is_di_second_alarm_end(pid,id,index,p_cond))
    {
      ret = true;
      break;
    }
  }

  return ret;
}
bool Ba::do_first_di_release(int pid,int id,int index,Condition** p_cond)
{
    bool ret;

    ret = false;
    for(auto & cond : allConditions)
    {
      if(cond->do_first_di_release(pid,id,index,p_cond))
      {
        ret = true;
        break;
      }
    }

    return ret;
}

bool Ba::is_first_cond_di(int pid,int id,int index,Condition** p_cond)
{
    bool ret;

    cout<<__func__<<" "<<pid<<" "<<id<<" "<<index<<endl;
    ret = false;
    for(auto & cond : allConditions)
    {
      if(cond->is_first_cond_di(pid,id,index,p_cond))
      {
        ret = true;
        break;
      }
    }

    return ret;
}
bool Ba::is_second_di(int pid,int id,int index,Condition** p_cond)
{
    bool ret;

    ret = false;
    for(auto & cond : allConditions)
    {
      if(cond->is_second_di(pid,id,index,p_cond))
      {
        ret = true;
        break;
      }
    }

    return ret;
}

void Ba::lookup_device(bool is_rs485)
{
    if(is_rs485)
    {
      for(auto& dev : allRS485Devs)
      {
        dev->lookup();
      }

      return;
    }
    for(auto& dev : allDevs)
    {
      dev->lookup();
    }
}

bool Ba::find_device_and_process(int fd,bool is_rs485)
{
  bool ret;

  ret = false;

  if(is_rs485)
  {
    for(auto& dev : allRS485Devs)
    {
      if(dev->is_event(fd))
      {
        dev->process_device(fd);
        ret = true;
        break;
      }

    }

    return ret;
  }
  for(auto& dev : allDevs)
  {
    // cout << "devs~!!!!!!!!!!!!:" <<dev->getId() << endl;
    if(dev->is_event(fd))
    {
      dev->process_device(fd);
      ret = true;
      break;
    }

  }


  return ret;
}

void Ba::main_timeout(int timer,bool is_rs485)
{
  if(is_rs485)
  {
    for(auto& dev : allRS485Devs)
    {
        dev->main_timeout();
    }
    return;
  }

  for(auto& dev : allDevs)
  {
      dev->main_timeout();
  }
  save_monitor_job();
  for(auto& cond : allConditions)
  {
    cond->main_timeout();
  }

  if(allSendMsgs.size())
  for(vector<WSendMsg>::iterator iter=allSendMsgs.begin(); iter!=allSendMsgs.end();iter++ )
  {
       if(iter->resend())
       {
            iter = allSendMsgs.erase(iter);

            break;
        }

  }

}

void Ba::ip_timeout(int timer)
{
  for(auto& dev : ipDevices)
  {
      dev->main_timeout();
  }


}

void Ba::phone_timeout(int timer)
{
  for(auto& dev : phoneDevices)
  {
      dev->main_timeout();
  }


}

void Ba::alarmMessage(string name,EDIOState status)
{
    std::stringstream ss;

    ss << name;

    if(status == EDIOState::ALARM)
    {
      ss << " 警報 ";
    }
    else
    {
      ss << " 正常 ";
    }

    using std::chrono::system_clock;
    std::time_t tt = system_clock::to_time_t (system_clock::now());

    struct std::tm * ptm = std::localtime(&tt);

    //ss << std::put_time(ptm, "%H:%M:%S");
    ss << ptm->tm_hour<<":"<<ptm->tm_min<<":"<<ptm->tm_sec;
    //ss << std::put_time(ptm, "%c");
    string callee;

    callee = get_callee_list();
  	string in = "desc="+ss.str()+"&callee="+callee;//input, transfer msg to server side

    sendSIPMessage(in);

}
void Ba::do_caller(string caller,string callee)
{

    std::stringstream ss;

    using std::chrono::system_clock;
    std::time_t tt = system_clock::to_time_t (system_clock::now());

    struct std::tm * ptm = std::localtime(&tt);


    ss << "callee="<<callee<<"&msg=警報 ";
    ss << ptm->tm_hour<<":"<<ptm->tm_min<<":"<<ptm->tm_sec;
    ss <<"&domain="<<myAcc.domain;

    string in = "call="+caller+"&"+ss.str();//input, transfer msg to server side

    sendSIPMessage(in);
}
void Ba::sendBaSIPMessage(string json)
{
  std::stringstream ss;

  ss << "/index.php?option=\"com_floor&task=sroots.update_sip\" ";

  WSendMsg msg;

  msg.set(ss.str(),"/tmp/siplog","/tmp/SIPLOG",true,true);
  msg.setJson(json);
  add_sendMessage(&msg);
}
void Ba::sendSIPMessage(string msg)
{
    cout <<"\n"<<typeid(*this).name()<<"::"<<__func__<<" "<<msg<<endl;
    fflush(stdout);

    int socketfd;

    socketfd = socket(AF_UNIX, SOCK_STREAM, 0);

    if (socketfd < 0) {
        cout<< "---Failed: socket() failed! Reason: " <<endl;
        fflush(stdout);
        return;
    }

    struct sockaddr_un addr;

    memset(&addr,0,sizeof(addr));
    addr.sun_family = AF_UNIX;
    strcpy(addr.sun_path,"/tmp/phpsocket");
    //socket_connect
    int result = connect(socketfd,(struct sockaddr *)&addr,sizeof(addr));

    if (result < 0) {
        cout<< "---Failed: socket_connect() failed! Reason: "<<endl;
        fflush(stdout);

       //Close
       close(socketfd);
       return;
    }

    //socket_write
    if(!write(socketfd, msg.c_str(), strlen(msg.c_str()))) {
        cout << "---Failed: socket_write() failed! Reason: "<<endl;
        fflush(stdout);
    }

    //Close
    close(socketfd);

}

void Ba::sendIPAlarm(EDIOState ipstatus,map_int_string& ids_map)
{
  std::stringstream ss;

  for(auto& id_map : ids_map)
  {
      ss <<  id_map.second<<",";
  }

  if(ipstatus == EDIOState::ALARM)
  {
    ss << " 警報 ";
  }
  else
  {
    ss << " 正常 ";
  }

  using std::chrono::system_clock;
  std::time_t tt = system_clock::to_time_t (system_clock::now());

  struct std::tm * ptm = std::localtime(&tt);

  ss << ptm->tm_hour<<":"<<ptm->tm_min<<":"<<ptm->tm_sec;

  string in;

	in = "desc="+ss.str()+"&callee="+get_callee_list();//inpu

  sendSIPMessage(in);
}

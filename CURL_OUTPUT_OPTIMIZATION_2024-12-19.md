# CURL 輸出檔案優化 - 2024-12-19

## 修改目的
移除不必要的 CURL/WGET 輸出檔案，減少 IO 負擔和硬碟競爭，提升系統效能。

## 修改範圍
根據程式碼分析，識別出只用於 LOG 記錄而沒有被 `readAllToJson` 讀取的 CURL 輸出檔案，並將其移除。

## 修改檔案清單

### 1. 24dio/src/Ba.cpp
- **sendDoorCardMessage()**: 移除 `-o /tmp/DOORCARD` 輸出
- **waitRecord()**: 移除 `-o /tmp/WAITRECORD` 輸出  
- **getAndSetSOSData()**: 移除 `-o SOSMAPPINGFILE` 輸出

### 2. 24dio/src/utility.cpp
- **check_ipaddr()**: 移除 `-o /tmp/MYACC` 輸出，只保留連線狀態檢查功能

### 3. 24dio/src/WSendMsg.cpp
- **do_send_ipaddr()**: 移除 `-o LOG_ipaddr` 輸出（JSON 和非 JSON 模式）
- **do_send()**: 移除 `-o /tmp/jsonres` 輸出
- **send_to_marco_service()**: 移除 `-o 127.0.0.1:5888` 輸出

### 4. 24dio/src/client.cpp
- **idle_loop()**: 移除 alive 檢查的 `-o 127.0.0.1:5888` 輸出

### 5. web/com_floor-1.0.0/site/helpers/floor.php
- **sendRecordMsg()**: 移除 `-o /tmp/REC` 輸出
- **send_to_center()**: 移除 `-o /tmp/DO_ACTION` 輸出

### 6. web/com_top-1.0.0/site/helpers/toputility.php
- **getCCTVDev()**: 移除 `-o /tmp/GETCCTVDEV` 輸出，改用 `shell_exec()` 直接獲取結果

## 保留的重要檔案
以下檔案因為有被 `readAllToJson` 讀取，所以保留不變：
- DIO24DevFILE (`/tmp/24DIO`)
- AlarmFILE (`/tmp/ALARMFILE`)
- ConditionFILE (`/tmp/CONDITIONFILE`)
- MSGCALLEEFILE (`/tmp/MSGCALLEEFILE`)
- MYACCOUNTFILE (`/tmp/MYACCOUNTFILE`)
- IPFILE (`/tmp/IPFILE`)
- PHONEFILE (`/tmp/PHONEFILE`)
- TIMINGFILE (`/tmp/TIMINGFILE`)
- DOORCARDFILE (`/tmp/DOORCARDFILE`)
- BAIPFILE (`/tmp/BAIPFILE`)
- elecNodeFILE (`/root/ELECNODEFILE`)
- phoneDev.cpp 中的 `/tmp/POST` 和 `/tmp/STATUS1`

## 預期效果
1. **減少 IO 操作**: 移除約 10+ 個不必要的檔案寫入操作
2. **降低硬碟負擔**: 減少 `/tmp/` 目錄的檔案競爭
3. **提升效能**: 減少檔案系統的讀寫壓力
4. **節省空間**: 避免產生不必要的暫存檔案

## 風險評估
- **低風險**: 所有移除的檔案都只用於 LOG 記錄，不影響程式邏輯
- **向後相容**: 程式功能完全保持不變
- **可回復**: 如有需要可輕易恢復原始的檔案輸出

## 測試建議
1. 確認程式啟動正常
2. 檢查各項功能運作正常
3. 監控系統 IO 使用率是否有改善
4. 確認 `/tmp/` 目錄下不再產生被移除的檔案

## 備註
此次優化專注於移除不必要的檔案 IO，對於需要讀取的重要資料檔案保持原有機制不變，確保系統穩定性。
